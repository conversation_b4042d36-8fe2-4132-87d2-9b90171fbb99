'use client';

import { useState, useCallback, useEffect } from 'react';
import { useDropzone } from 'react-dropzone';
import { FiUpload, FiImage, FiDownload, FiArrowUp, FiX, FiCheck } from 'react-icons/fi';
import { Breadcrumb } from '@/components/Breadcrumb';
import Link from 'next/link';
import Head from 'next/head';
import Script from 'next/script';
import RelatedTools from '@/components/RelatedTools';

interface FileItem {
  id: string;
  name: string;
  size: number;
  status: 'waiting' | 'converting' | 'done' | 'error';
  downloadUrl?: string;
  errorMessage?: string;
  file: File;
}

export default function Home() {
  const [files, setFiles] = useState<FileItem[]>([]);
  const [quality, setQuality] = useState(85);
  const [isConverting, setIsConverting] = useState(false);
  const [removeExif, setRemoveExif] = useState(false);
  const [consentPrivacy, setConsentPrivacy] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [convertProgress, setConvertProgress] = useState({ current: 0, total: 0 });
  const [showScrollTop, setShowScrollTop] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Add canonical link
  useEffect(() => {
    // Check if a canonical link already exists
    let link = document.querySelector('link[rel="canonical"]');
    
    // If not, create and add it
    if (!link) {
      link = document.createElement('link');
      link.setAttribute('rel', 'canonical');
      link.setAttribute('href', 'https://heic-tojpg.com/');
      document.head.appendChild(link);
    } else {
      // If it exists, make sure it has the correct href
      link.setAttribute('href', 'https://heic-tojpg.com/');
    }
  }, []);

  // Detect if device is mobile after component mount
  useEffect(() => {
    setIsMobile(/mobile|android|ios/i.test(window.navigator.userAgent));
  }, []);

  // Track scroll position to show/hide back to top button
  useEffect(() => {
    const handleScroll = () => {
      setShowScrollTop(window.scrollY > 400);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const onDrop = useCallback((acceptedFiles: File[]) => {
    setError(null);
    
    // Validate file types
    const invalidFiles = acceptedFiles.filter(file => {
      const lowerName = file.name.toLowerCase();
      return !(
        lowerName.endsWith('.heic') || 
        lowerName.endsWith('.heif') ||
        lowerName.endsWith('.jpg') ||
        lowerName.endsWith('.jpeg') ||
        lowerName.endsWith('.png')
      );
    });
    
    if (invalidFiles.length > 0) {
      setError('Please only upload HEIC, HEIF, JPEG or PNG files.');
      return;
    }

    const newFiles = acceptedFiles.map(file => ({
      id: Math.random().toString(36).substring(7),
      name: file.name,
      size: file.size,
      status: 'waiting' as const,
      file: file
    }));
    setFiles(prev => [...prev, ...newFiles]);
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/heic': ['.heic', '.HEIC'],
      'image/heif': ['.heif', '.HEIF'],
      'image/jpeg': ['.jpg', '.jpeg', '.JPG', '.JPEG'],
      'image/png': ['.png', '.PNG']
    },
    maxFiles: 100,
    noDrag: isMobile,
    onDropRejected: () => {
      setError('Please only upload HEIC, HEIF, JPEG or PNG files.');
    },
    onError: (error) => {
      setError('An error occurred while uploading files.');
      console.error('Dropzone error:', error);
    }
  });

  const handleConvert = async () => {
    if (!consentPrivacy) {
      setError('Please consent to the privacy policy before converting files.');
      return;
    }

    if (files.length === 0) {
      setError('Please add some files to convert.');
      return;
    }

    setError(null);
    setIsConverting(true);
    const filesToConvert = files.filter(f => f.status === 'waiting');
    setConvertProgress({ current: 0, total: filesToConvert.length });
    
    try {
      await Promise.all(filesToConvert.map(async (fileItem, index) => {
        setFiles(prev => 
          prev.map(f => 
            f.id === fileItem.id 
              ? { ...f, status: 'converting' } 
              : f
          )
        );
        setConvertProgress(prev => ({ ...prev, current: index + 1 }));

        try {
          const formData = new FormData();
          formData.append('file', fileItem.file);
          formData.append('quality', quality.toString());
          formData.append('removeExif', removeExif.toString());

          const response = await fetch('/api/heic-to-jpg', {
            method: 'POST',
            body: formData,
          });

          const result = await response.json();

          if (!response.ok) {
            throw new Error(result.error || 'Conversion failed');
          }

          setFiles(prev => 
            prev.map(f => 
              f.id === fileItem.id 
                ? { ...f, status: 'done', downloadUrl: result.url } 
                : f
            )
          );
        } catch (error: any) {
          console.error('File conversion error:', error);
          const errorMessage = error.message || 'Failed to convert file';
          setFiles(prev => 
            prev.map(f => 
              f.id === fileItem.id 
                ? { ...f, status: 'error', errorMessage: errorMessage } 
                : f
            )
          );
          setError(`Error converting ${fileItem.name}: ${errorMessage}`);
        }
      }));
    } catch (error: any) {
      console.error('Batch conversion error:', error);
      setError('An error occurred during batch conversion. Please try again.');
    } finally {
      setIsConverting(false);
    }
  };

  const handleDownloadAll = async () => {
    const completedFiles = files.filter(f => f.status === 'done' && f.downloadUrl);
    if (completedFiles.length === 0) {
      setError('No converted files to download');
      return;
    }

    setError(null);
    try {
      // Create a hidden download link to trigger the download
      for (const file of completedFiles) {
        const link = document.createElement('a');
        link.href = file.downloadUrl!;
        link.download = file.name.replace('.heic', '').replace('.HEIC', '') + '.jpg';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        // Add small delay to avoid browser blocking multiple downloads
        await new Promise(resolve => setTimeout(resolve, 500));
      }
    } catch (error) {
      console.error('Download error:', error);
      setError('Failed to download some files. Please try again.');
    }
  };

  const handleClearAll = () => {
    setFiles([]);
    setError(null);
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Back to top function
  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };

  const handleDeleteFile = (fileId: string) => {
    setFiles(prev => prev.filter(file => file.id !== fileId));
  };

  return (
    <>
      <main className="min-h-screen p-4 md:p-8 max-w-4xl mx-auto relative bg-gradient-to-b from-slate-50/80 via-white to-white">
        <Breadcrumb />
        <h1 className="text-2xl md:text-4xl font-bold text-center mb-4 md:mb-8 text-gray-800">
          The Best Free HEIC to JPG Converter
        </h1>
        <p className="text-center text-gray-600 mb-6">
          Convert iPhone HEIC photos to JPEG format online
        </p>

          {error && (
            <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg">
              <p className="text-red-600">{error}</p>
            </div>
          )}

          <div className="mb-6 md:mb-8 relative">
            <div
              {...getRootProps()}
              className={`border-2 border-dashed rounded-lg p-6 md:p-8 text-center cursor-pointer transition-all duration-300 relative backdrop-blur-sm
                ${isDragActive 
                  ? 'border-indigo-500 bg-gradient-to-br from-indigo-50/40 to-white/80' 
                  : 'border-gray-300 hover:border-indigo-400 hover:bg-gradient-to-br hover:from-slate-50/50 hover:to-white/90'
                } shadow-sm hover:shadow-md`}
            >
              <input {...getInputProps()} accept=".heic,.HEIC,.heif,.HEIF,.jpg,.jpeg,.JPG,.JPEG,.png,.PNG" />
              <FiImage className="mx-auto text-4xl md:text-5xl mb-3 text-indigo-400" />
              <p className="text-lg md:text-xl mb-2 text-gray-800">Select HEIC/HEIF/JPEG/PNG Photos</p>
              <p className="text-sm text-gray-600 mb-2">from your iPhone or device</p>
              <p className="text-xs text-gray-500">Supports up to 100 files</p>
              
              {isMobile && (
                <div className="mt-4 text-sm text-indigo-600 font-medium">
                  Tap here to select photos from your device
                </div>
              )}
            </div>
            
            {/* Buy me a coffee button outside the dropzone in the left bottom corner */}
            <a
              href="https://ko-fi.com/yourfriendlycreator"
              target="_blank"
              rel="noopener noreferrer"
              className="absolute -bottom-10 left-0 px-2 py-1 md:px-3 md:py-2 text-xs md:text-sm border border-transparent rounded-md shadow-sm text-white bg-gradient-to-r from-pink-500 to-pink-600 hover:from-pink-600 hover:to-pink-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-pink-500 transition-all duration-200 flex items-center justify-center"
            >
              <span>☕Buy me a coffee</span>
            </a>
            <div className="absolute -bottom-10 left-[140px] md:left-[160px] text-xs md:text-sm text-gray-600 italic">
              I'll use the money to upgrade to a better server to help you with daily work.
            </div>
          </div>
          
          {files.length > 0 && (
            <div className="space-y-4 md:space-y-6">
              <div className="bg-white rounded-lg shadow-md overflow-x-auto border border-gray-100">
                <table className="min-w-full table-fixed">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="w-[10%] px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">#</th>
                      <th className="w-[30%] sm:w-[40%] px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">File</th>
                      <th className="w-[25%] sm:w-[20%] px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                      <th className="w-[25%] sm:w-[20%] px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Size</th>
                      <th className="w-[10%] px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Action</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {files.map((file, index) => (
                      <tr key={file.id}>
                        <td className="w-[10%] px-4 py-3 whitespace-nowrap text-sm text-gray-500">{index + 1}</td>
                        <td className="w-[30%] sm:w-[40%] px-4 py-3 text-sm font-medium text-gray-900">
                          <div className="truncate" title={file.name}>
                            {isMobile ? 
                              file.name.length > 10 ? 
                                file.name.slice(0, 7) + '...' + file.name.slice(-3) 
                                : file.name
                              : file.name
                            }
                          </div>
                        </td>
                        <td className="w-[25%] sm:w-[20%] px-4 py-3 whitespace-nowrap">
                          {file.status === 'done' && file.downloadUrl ? (
                            <a 
                              href={file.downloadUrl} 
                              className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                              download
                            >
                              Download
                            </a>
                          ) : file.status === 'error' ? (
                            <span className="text-red-500 text-sm" title={file.errorMessage}>Error</span>
                          ) : file.status === 'converting' ? (
                            <span className="text-yellow-500 text-sm">Converting...</span>
                          ) : (
                            <span className="text-gray-500 text-sm">Waiting</span>
                          )}
                        </td>
                        <td className="w-[25%] sm:w-[20%] px-4 py-3 whitespace-nowrap text-sm text-gray-500">{formatFileSize(file.size)}</td>
                        <td className="w-[10%] px-4 py-3 whitespace-nowrap text-center">
                          {file.status === 'done' ? (
                            <FiCheck className="text-green-500 w-5 h-5 mx-auto" title="Conversion Complete" />
                          ) : (
                            <button 
                              onClick={() => handleDeleteFile(file.id)}
                              className="text-gray-500 hover:text-red-600 transition-colors duration-200 focus:outline-none"
                              disabled={file.status === 'converting'}
                              title="Delete File"
                            >
                              <FiX className="w-5 h-5" />
                            </button>
                          )}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              <div className="bg-gradient-to-br from-slate-50/90 via-white to-white p-4 md:p-6 rounded-lg shadow-md border border-gray-100">
                <h2 className="text-lg md:text-xl font-semibold mb-4 text-gray-800">Output Settings</h2>
                <div className="grid gap-4 md:grid-cols-2">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Format:</label>
                    <select
                      className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                      value="JPG"
                      disabled
                    >
                      <option>JPG</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Quality:</label>
                    <div className="flex items-center space-x-2">
                      <input
                        type="range"
                        min="1"
                        max="100"
                        value={quality}
                        onChange={(e) => setQuality(parseInt(e.target.value))}
                        className="flex-1"
                      />
                      <span className="text-sm text-gray-600 w-12">{quality}%</span>
                    </div>
                  </div>
                </div>

                <div className="mt-4">
                  <label className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={removeExif}
                      onChange={(e) => setRemoveExif(e.target.checked)}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="text-sm text-gray-700">Remove all EXIF information</span>
                  </label>
                </div>
              </div>

              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <input
                    type="checkbox"
                    checked={consentPrivacy}
                    onChange={(e) => setConsentPrivacy(e.target.checked)}
                    className="mt-1 w-5 h-5 md:w-4 md:h-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                    required
                  />
                  <label className="text-sm text-gray-600">
                    I consent to heic-tojpg.com collecting and processing my data according to{' '}
                    <Link href="/privacy-policy" className="text-indigo-600 hover:text-indigo-800 underline">
                      Privacy Policy
                    </Link>
                    .
                  </label>
                </div>

                <div className="flex flex-col sm:flex-row gap-2 sm:justify-end sm:space-x-3">
                  <button
                    onClick={handleClearAll}
                    className="w-full sm:w-auto px-3 py-2 md:px-4 md:py-2 text-sm border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200"
                    disabled={isConverting}
                  >
                    Clear all
                  </button>
                  <button
                    onClick={handleConvert}
                    disabled={isConverting || !files.some(f => f.status === 'waiting') || !consentPrivacy}
                    className="w-full sm:w-auto px-3 py-2 md:px-4 md:py-2 text-sm border border-transparent rounded-md shadow-sm text-white bg-gradient-to-r from-indigo-600 to-indigo-700 hover:from-indigo-700 hover:to-indigo-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                  >
                    {isConverting ? 'Converting...' : 'Convert'}
                  </button>
                  <button
                    onClick={handleDownloadAll}
                    disabled={!files.some(f => f.status === 'done')}
                    className="w-full sm:w-auto px-3 py-2 md:px-4 md:py-2 text-sm border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-1 transition-colors duration-200"
                  >
                    <FiDownload className="w-4 h-4" />
                    <span>Download all</span>
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Share buttons */}
          <div className="mt-12 p-4 bg-gradient-to-br from-slate-50/90 via-white to-white rounded-lg shadow-md border border-gray-100">
            <h3 className="text-lg font-semibold mb-4 text-gray-800">Share with Friends</h3>
            <p className="text-sm text-gray-600 mb-4">If you found this tool useful, please share it with friends who might need it!</p>
            <div className="sharethis-inline-share-buttons"></div>
          </div>

          {/* Related tools */}
          <RelatedTools currentTool="HEIC to JPG" />

          {/* Conversion progress indicator */}
          {isConverting && isMobile && (
            <div className="fixed bottom-4 left-0 right-0 mx-4 bg-gradient-to-r from-indigo-50/90 via-white to-blue-50/90 p-4 rounded-lg shadow-lg border border-indigo-100 backdrop-blur-sm">
              <div className="text-center text-sm text-indigo-800 font-medium">
                Converting: {convertProgress.current} of {convertProgress.total} files
              </div>
              <div className="mt-2 h-2 bg-indigo-100 rounded-full overflow-hidden">
                <div 
                  className="h-full bg-gradient-to-r from-indigo-600 to-indigo-500 transition-all duration-300" 
                  style={{ width: `${(convertProgress.current / convertProgress.total) * 100}%` }}
                />
              </div>
            </div>
          )}

          {showScrollTop && (
            <button
              onClick={scrollToTop}
              className="fixed bottom-6 right-6 p-3 bg-gradient-to-r from-indigo-600 to-indigo-700 text-white rounded-full shadow-lg hover:from-indigo-700 hover:to-indigo-800 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 z-50"
              aria-label="Scroll to top"
            >
              <FiArrowUp className="w-6 h-6" />
            </button>
          )}

          <div className="mt-12 space-y-16">
            <section className="introduction">
              <h2 className="text-2xl font-bold mb-6 text-gray-800">Heic to JPG Converter Features</h2>
              <div className="space-y-16">
                {/* Feature Group 1: Free & Easy + Fast Conversion */}
                <div className="grid md:grid-cols-2 gap-8 items-center">
                  <div className="space-y-8">
                    <div>
                      <h3 className="text-lg font-semibold mb-3 text-gray-800 flex items-center">
                        <span className="bg-indigo-100 w-8 h-8 rounded-full flex items-center justify-center mr-3 flex-shrink-0">1</span>
                        HEIC to JPG Made Easy & 100% Free
                      </h3>
                      <ul className="space-y-2 text-gray-600 list-disc pl-11">
                        <li>100% free to use, no registration or payment required</li>
                        <li>Simple and intuitive interface with drag-and-drop or click-to-upload functionality</li>
                      </ul>
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold mb-3 text-gray-800 flex items-center">
                        <span className="bg-indigo-100 w-8 h-8 rounded-full flex items-center justify-center mr-3 flex-shrink-0">2</span>
                        Convert HEIC to JPG Fast & in High Quality
                      </h3>
                      <ul className="space-y-2 text-gray-600 list-disc pl-11">
                        <li>Efficient algorithm for quick HEIC file conversion, saving your time</li>
                        <li>High-quality JPG output maintaining original resolution and color details</li>
                      </ul>
                    </div>
                  </div>
                  <div className="relative">
                    <img 
                      src="https://image.heic-tojpg.com/heic-to-jpg-converter.jpg" 
                      alt="Heic to Jpg Converter" 
                      className="rounded-lg shadow-lg w-full h-[400px] object-cover"
                    />
                  </div>
                </div>

                {/* Feature Group 2: Security & Privacy */}
                <div className="grid md:grid-cols-2 gap-8 items-center">
                  <div className="relative order-2 md:order-1">
                    <img 
                      src="https://image.heic-tojpg.com/the-best-free-heic-to-jpg-converter.jpg" 
                      alt="The Best Free HEIC to JPG Converter" 
                      className="rounded-lg shadow-lg w-full h-[400px] object-cover"
                    />
                  </div>
                  <div className="space-y-8 order-1 md:order-2">
                    <div>
                      <h3 className="text-lg font-semibold mb-3 text-gray-800 flex items-center">
                        <span className="bg-indigo-100 w-8 h-8 rounded-full flex items-center justify-center mr-3 flex-shrink-0">3</span>
                        Convert HEIC to JPG with No Watermark & No Limits
                      </h3>
                      <ul className="space-y-2 text-gray-600 list-disc pl-11">
                        <li>Converted JPG files are watermark-free, ready for social media, websites, or printing</li>
                        <li>There are no file size or quantity limits, use anytime, anywhere</li>
                      </ul>
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold mb-3 text-gray-800 flex items-center">
                        <span className="bg-indigo-100 w-8 h-8 rounded-full flex items-center justify-center mr-3 flex-shrink-0">4</span>
                        HEIC to JPG – 100% Secure & Privacy Protected
                      </h3>
                      <ul className="space-y-2 text-gray-600 list-disc pl-11">
                        <li>End-to-end encryption technology ensures file security</li>
                        <li>No storage of uploaded HEIC files, immediate deletion after conversion</li>
                      </ul>
                    </div>
                  </div>
                </div>

                {/* Feature Group 3: Batch Processing & Compatibility */}
                <div className="grid md:grid-cols-2 gap-8 items-center">
                  <div className="space-y-8">
                    <div>
                      <h3 className="text-lg font-semibold mb-3 text-gray-800 flex items-center">
                        <span className="bg-indigo-100 w-8 h-8 rounded-full flex items-center justify-center mr-3 flex-shrink-0">5</span>
                        Fast & Efficient HEIC to JPG Batch Conversion
                      </h3>
                      <ul className="space-y-2 text-gray-600 list-disc pl-11">
                        <li>Convert multiple HEIC files simultaneously for improved efficiency</li>
                        <li>Perfect for processing iPhone photo batches without individual operations</li>
                      </ul>
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold mb-3 text-gray-800 flex items-center">
                        <span className="bg-indigo-100 w-8 h-8 rounded-full flex items-center justify-center mr-3 flex-shrink-0">6</span>
                        Seamless HEIC to JPG Conversion Across All Platforms
                      </h3>
                      <ul className="space-y-2 text-gray-600 list-disc pl-11">
                        <li>It supports all modern browsers (Chrome, Firefox, Edge, Safari, etc.)</li>
                        <li>Compatible with Windows, macOS, Linux, Android, and iOS devices</li>
                      </ul>
                    </div>
                  </div>
                  <div className="relative">
                    <img 
                      src="https://cdn.pixabay.com/photo/2016/11/30/20/58/programming-1873854_1280.png" 
                      alt="Batch processing and compatibility" 
                      className="rounded-lg shadow-lg w-full h-[400px] object-cover"
                    />
                  </div>
                </div>

                {/* Feature Group 4: Quality Control & Online Access */}
                <div className="grid md:grid-cols-2 gap-8 items-center">
                  <div className="relative order-2 md:order-1">
                    <img 
                      src="https://cdn.pixabay.com/photo/2015/07/17/22/43/student-849825_1280.jpg" 
                      alt="Quality control and online access" 
                      className="rounded-lg shadow-lg w-full h-[400px] object-cover"
                    />
                  </div>
                  <div className="space-y-8 order-1 md:order-2">
                    <div>
                      <h3 className="text-lg font-semibold mb-3 text-gray-800 flex items-center">
                        <span className="bg-indigo-100 w-8 h-8 rounded-full flex items-center justify-center mr-3 flex-shrink-0">7</span>
                        HEIC to JPG Converter with Adjustable Image Quality
                      </h3>
                      <ul className="space-y-2 text-gray-600 list-disc pl-11">
                        <li>Adjustable JPG quality options for high-quality or compressed versions</li>
                        <li>Customizable output resolution for storage optimization</li>
                      </ul>
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold mb-3 text-gray-800 flex items-center">
                        <span className="bg-indigo-100 w-8 h-8 rounded-full flex items-center justify-center mr-3 flex-shrink-0">8</span>
                        Online HEIC to JPG Converter – No Installation Required
                      </h3>
                      <ul className="space-y-2 text-gray-600 list-disc pl-11">
                        <li>Pure online conversion, no software or plugins needed</li>
                        <li>Works directly in the browser, instant access from any device</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </section>

            <section className="what-is">
              <h2 className="text-2xl font-bold mb-6 text-gray-800">What is the Best HEIC to JPG converter</h2>
              
              <div className="space-y-16">
                {/* HEIC Introduction Group */}
                <div className="grid md:grid-cols-2 gap-8 items-center">
                  <div className="space-y-8">
                    <div>
                      <h3 className="text-lg font-semibold mb-3 text-gray-800">What is a HEIC file to JPG</h3>
                      <p className="text-gray-600">
                        A HEIC to JPG conversion is the process of transforming Apple&apos;s High Efficiency Image Format (HEIC) 
                        into the universally compatible JPEG format. This conversion maintains image quality while making your photos accessible across all devices and platforms.
                      </p>
                     
                    </div>

                    <div>
                      <h3 className="text-lg font-semibold mb-3 text-gray-800">What is HEIF format?</h3>
                      <p className="text-gray-600">
                       HEIF (High Efficiency Image Format) is a modern image container format that offers superior compression while maintaining high image quality. It can store images with better quality than JPEG at half the file size. To convert HEIF images to more widely compatible formats, you can use a HEIC to JPG converter for easy and fast image conversion. For more details, you can visit the <a href="https://en.wikipedia.org/wiki/High_Efficiency_Image_File_Format" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:text-blue-800 underline">Wikipedia page on HEIF</a>.
                      </p>
                    </div>
                  </div>
                  <div className="relative">
                    <img 
                      src="https://image.heic-tojpg.com/what-is-heif-format.jpg" 
                      alt="What is HEIF format" 
                      className="rounded-lg shadow-lg w-full h-[400px] object-cover"
                    />
                  </div>
                </div>

                {/* HEIC Details Group */}
                <div className="grid md:grid-cols-2 gap-8 items-center">
                  <div className="relative order-2 md:order-1">
                    <img 
                      src="https://image.heic-tojpg.com/what-is-heic-file.jpg" 
                      alt="What is HEIC file" 
                      className="rounded-lg shadow-lg w-full h-[400px] object-cover"
                    />
                  </div>
                  <div className="space-y-8 order-1 md:order-2">
                    <div>
                      <h3 className="text-lg font-semibold mb-3 text-gray-800">What is a HEIC file?</h3>
                      <p className="text-gray-600">
                       HEIC is Apple&apos;s implementation of the HEIF format. When you take photos on an iPhone or iPad, they&apos;re saved as HEIC files by default. These files offer excellent quality and small size but may not be compatible with all devices. To ensure compatibility, you can easily convert HEIC files to JPG using a HEIC to JPG converter. For more details, you can visit the <a href="https://en.wikipedia.org/wiki/HEIC" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:text-blue-800 underline">Wikipedia page on HEIC</a>.
                      </p>
                    </div>

                    <div>
                      <h3 className="text-lg font-semibold mb-3 text-gray-800">What is Exif metadata?</h3>
                      <p className="text-gray-600">
                       EXIF (Exchangeable Image File Format) metadata contains information about your photos, including camera settings, date taken, location, and device information. When converting HEIC files to JPG, our HEIC to JPG converter allows you to choose whether to keep or remove this metadata during the conversion process. For more details, you can visit the <a href="https://en.wikipedia.org/wiki/Exif" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:text-blue-800 underline">Wikipedia page on Exif</a>.
                      </p>
                    </div>
                  </div>
                </div>

                {/* JPEG Group */}
                <div className="grid md:grid-cols-2 gap-8 items-center">
                  <div className="space-y-8">
                    <div>
                      <h3 className="text-lg font-semibold mb-3 text-gray-800">What is JPEG?</h3>
                      <p className="text-gray-600">
                       JPEG is the most widely used image format, supported by virtually all devices and platforms. It uses lossy compression to create smaller file sizes while maintaining good image quality, making it perfect for photos and complex images with many colors. If you&apos;re looking to convert HEIC images to a more compatible format, using a HEIC to JPG converter is the ideal solution. For more details, you can visit the <a href="https://en.wikipedia.org/wiki/JPEG" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:text-blue-800 underline">Wikipedia page on JPEG</a>.
                      </p>
                    </div>

                    <div>
                      <h3 className="text-lg font-semibold mb-3 text-gray-800">What image formats can I upload?</h3>
                      <p className="text-gray-600">
                       Currently, our HEIC to JPG converter supports HEIC/HEIF files for conversion. These formats are commonly used by iOS devices (iPhone and iPad) when taking photos. Convert your HEIC images to JPG for better compatibility with other devices and platforms.
                      </p>
                    </div>
                  </div>
                  <div className="relative">
                    <img 
                      src="https://image.heic-tojpg.com/what-is-jpeg.jpg" 
                      alt="What is JPEG" 
                      className="rounded-lg shadow-lg w-full h-[400px] object-cover"
                    />
                  </div>
                </div>

                {/* Conversion Options Group */}
                <div className="grid md:grid-cols-2 gap-8 items-center">
                  <div className="relative order-2 md:order-1">
                    <img 
                      src="https://image.heic-tojpg.com/convert-heic-to-jpg-in-seconds.jpg" 
                      alt="Convert Heic to Jpg in Seconds" 
                      className="rounded-lg shadow-lg w-full h-[400px] object-cover"
                    />
                  </div>
                  <div className="space-y-8 order-1 md:order-2">
                    <div>
                      <h3 className="text-lg font-semibold mb-3 text-gray-800">What image formats can I convert to?</h3>
                      <p className="text-gray-600">
                       Our HEIC to JPG converter transforms HEIC files into high-quality JPEG images. The JPEG format ensures maximum compatibility while maintaining good image quality and reasonable file sizes, making it the perfect choice for your converted images.
                      </p>
                    </div>

                    <div>
                      <h3 className="text-lg font-semibold mb-3 text-gray-800">Why convert HEIC to JPG?</h3>
                      <p className="text-gray-600">
                        Converting HEIC to JPG ensures your photos can be viewed, edited, and shared on any device or platform. 
                        While HEIC offers better compression, JPG provides universal compatibility with websites, applications, 
                        and devices that may not support the newer HEIC format.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </section>

            <section className="how-to">
              <h2 className="text-2xl font-bold mb-6 text-gray-800">How to Convert Heic to Jpg</h2>
              
              <div className="grid md:grid-cols-2 gap-8 items-center mb-12">
                <div>
                  <ol className="space-y-6 relative">
                    <div className="absolute top-0 bottom-0 left-4 w-0.5 bg-indigo-100"></div>
                    <li className="relative pl-12">
                      <div className="absolute left-0 bg-indigo-500 text-white w-8 h-8 rounded-full flex items-center justify-center font-semibold">1</div>
                      <h3 className="text-lg font-semibold mb-2 text-gray-800">Upload HEIC Files</h3>
                      <p className="text-gray-600">
                        Drag and drop your HEIC files into the conversion area, or click to select files from your device. Our converter accepts multiple files for batch processing.
                      </p>
                    </li>
                    <li className="relative pl-12">
                      <div className="absolute left-0 bg-indigo-500 text-white w-8 h-8 rounded-full flex items-center justify-center font-semibold">2</div>
                      <h3 className="text-lg font-semibold mb-2 text-gray-800">Choose Settings</h3>
                      <p className="text-gray-600">
                        Adjust conversion settings like output quality (from 1-100%). You can also choose to keep or remove EXIF data from your images for additional privacy.
                      </p>
                    </li>
                    <li className="relative pl-12">
                      <div className="absolute left-0 bg-indigo-500 text-white w-8 h-8 rounded-full flex items-center justify-center font-semibold">3</div>
                      <h3 className="text-lg font-semibold mb-2 text-gray-800">Convert & Download</h3>
                      <p className="text-gray-600">
                        Click the "Convert" button to start the HEIC to JPG conversion process. Once completed, you can download your JPG files individually or all at once using our bulk download option..
                      </p>
                    </li>
                  </ol>
                </div>
                <div className="relative">
                  <img 
                    src="https://cdn.pixabay.com/photo/2015/02/02/11/09/office-620822_1280.jpg" 
                    alt="Conversion Process" 
                    className="rounded-lg shadow-lg w-full h-[400px] object-cover"
                  />
                  {/*
                  <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent rounded-lg"></div>
                  <div className="absolute bottom-4 left-4 right-4 text-white text-center bg-black/30 backdrop-blur-sm p-3 rounded">
                    <p className="text-lg font-semibold">Simple 3-Step Process</p>
                    <p className="text-sm">No technical knowledge required</p>
                  </div>
                  */}
                </div>
              </div>
            </section>

            <section className="platform-guides space-y-16">
              {/* Windows Guide */}
              <div>
                <h2 className="text-2xl font-bold mb-6 text-gray-800"><a href="/posts/how-to-convert-heic-to-jpg-on-windows">How to Convert Heic to JPG on Windows</a></h2>
                <div className="grid md:grid-cols-2 gap-8 items-center">
                  <div className="space-y-4">
                    <ol className="list-decimal pl-5 space-y-3 text-gray-600">
                      <li>
                        <span className="font-medium">Using our online HEIC to JPG converter:</span> Open our HEIC to JPG converter website, upload your HEIC files, adjust settings, and download the converted JPGs.
                      </li>
                      <li>
                        <span className="font-medium">Using Windows Photos app:</span> Recent Windows 10/11 updates include HEIC support. Open the image in the Photos app, click "Edit & Create" {">"}  "Save as" {">"}  select "JPEG".
                      </li>
                      <li>
                        <span className="font-medium">Using Windows File Explorer:</span> Right-click the HEIC file, select "Open with" {">"}  Photos, then save as JPG format.
                      </li>
                    </ol>
                  </div>
                  <div className="relative">
                    <img 
                      src="https://cdn.pixabay.com/photo/2020/01/26/20/14/computer-4795762_1280.jpg" 
                      alt="How to Convert Heic to Jpg on Windows" 
                      className="rounded-lg shadow-lg w-full h-[300px] object-cover"
                    />
                  </div>
                </div>
              </div>
              
              {/* Mac Guide */}
              <div>
                <h2 className="text-2xl font-bold mb-6 text-gray-800">How to Convert Heic to Jpg on Mac</h2>
                <div className="grid md:grid-cols-2 gap-8 items-center">
                  <div className="relative order-2 md:order-1">
                    <img 
                      src="https://image.heic-tojpg.com/how-to-convert-heic-to-jpg-on-mac.jpg" 
                      alt="How to Convert Heic to Jpg on Mac" 
                      className="rounded-lg shadow-lg w-full h-[300px] object-cover"
                    />
                  </div>
                  <div className="space-y-4 order-1 md:order-2">
                    <ol className="list-decimal pl-5 space-y-3 text-gray-600">
                      <li>
                        <span className="font-medium">Using our online service:</span> Visit our HEIC to JPG converter website, upload your HEIC images, and easily convert them to JPG using our HEIC to JPG converter. Then, download your images as high-quality JPG files.
                      </li>
                      <li>
                        <span className="font-medium">Using Preview app:</span> Open the HEIC file in Preview, click "File" {">"}  "Export", select "JPEG" format, adjust quality if needed, and save.
                      </li>
                      <li>
                        <span className="font-medium">Using Photos app:</span> Import the HEIC image to Photos, select it, click File menu, then Export, then "Export Photo", choose JPG format, and export the image using the HEIC to JPG conversion option..
                      </li>
                    </ol>
                  </div>
                </div>
              </div>
              
              {/* iPhone Guide */}
              <div>
                <h2 className="text-2xl font-bold mb-6 text-gray-800">How to Convert Heic to Jpg on Iphone</h2>
                <div className="grid md:grid-cols-2 gap-8 items-center">
                  <div className="space-y-4">
                    <ol className="list-decimal pl-5 space-y-3 text-gray-600">
                      <li>
                        <span className="font-medium">Using our HEIC to JPG converter:</span> Open Safari, visit our HEIC to JPG converter website, select photos from your library, and save the JPG versions back to your device.
                      </li>
                      <li>
                        <span className="font-medium">Using the Photos app:</span> Open the image, tap "Share" {">"}  "Copy Photo", then paste into Notes or another app which automatically converts it to JPG.
                      </li>
                      <li>
                        <span className="font-medium">Changing camera settings:</span> Go to Settings {">"} Camera {">"} Formats and select "Most Compatible" to save new photos directly as JPG instead of HEIC.
                      </li>
                    </ol>
                  </div>
                  <div className="relative">
                    <img 
                      src="https://image.heic-tojpg.com/how-to-convert-heic-to-jpg-on-iphone.jpg" 
                      alt="How to Convert Heic to Jpg on Iphone" 
                      className="rounded-lg shadow-lg w-full h-[300px] object-cover"
                    />
                  </div>
                </div>
              </div>
              
              {/* Android Guide */}
              <div>
                <h2 className="text-2xl font-bold mb-6 text-gray-800">How to Convert Heic to Jpg on Android</h2>
                <div className="grid md:grid-cols-2 gap-8 items-center">
                  <div className="relative order-2 md:order-1">
                    <img 
                      src="https://image.heic-tojpg.com/how-to-convert-heic-to-jpg-on-android.jpeg" 
                      alt="How to Convert Heic to Jpg on Android" 
                      className="rounded-lg shadow-lg w-full h-[300px] object-cover"
                    />
                  </div>
                  <div className="space-y-4 order-1 md:order-2">
                    <ol className="list-decimal pl-5 space-y-3 text-gray-600">
                      <li>
                        <span className="font-medium">Using our HEIC to JPG converter:</span> Open the HEIC to JPG website, upload HEIC files, and download the converted JPGs.
                      </li>
                      <li>
                        <span className="font-medium">Using Google Photos:</span> Open the HEIC image in Google Photos, tap "Share" {">"}  "Save to Device" which typically saves as JPG format.
                      </li>
                      <li>
                        <span className="font-medium">Using Gallery app:</span> some Android devices allow you to open HEIC to JPG files and convert them through the Share or Export menu, making it easy to save your photos in a more compatible format.
                      </li>
                    </ol>
                  </div>
                </div>
              </div>
              
              {/* Windows 10/11 Guide */}
              <div>
                <h2 className="text-2xl font-bold mb-6 text-gray-800">How to Convert Heic to Jpg On Windows 10/11</h2>
                <div className="grid md:grid-cols-2 gap-8 items-center">
                  <div className="space-y-4">
                    <ol className="list-decimal pl-5 space-y-3 text-gray-600">
                      <li>
                        <span className="font-medium">Use our online HEIC to JPG converter (Recommended):</span> Simply upload your files at the top of this page, adjust quality settings if needed, and download your converted JPGs instantly.
                      </li>
                      <li>
                        <span className="font-medium">Install HEIF Image Extensions:</span> Alternatively, you can download the "HEIF Image Extensions" from the Microsoft Store.
                      </li>
                      <li>
                        <span className="font-medium">Open with Photos app</span> 
                      </li>
                      <li>
                        <span className="font-medium">Save as JPEG</span> 
                      </li>
                    </ol>
                    <div className="mt-4 p-3 bg-indigo-50 rounded-lg border border-indigo-100">
                      <p className="text-indigo-700 text-sm font-medium">
                        ✓ Pro tip: Our online HEIC to JPG converter saves time and handles multiple files at once, while the Windows method requires extensions and converting files one by one.
                      </p>
                    </div>
                  </div>
                  <div className="relative">
                    <img 
                      src="https://image.heic-tojpg.com/how-to-convert-heic-to-jpg-on-windows-10.jpeg" 
                      alt="How to Convert Heic to Jpg on Windows 10" 
                      className="rounded-lg shadow-lg w-full h-[300px] object-cover"
                    />
                  </div>
                </div>
              </div>
              
              {/* Google Drive Guide */}
              <div>
                <h2 className="text-2xl font-bold mb-6 text-gray-800">How to convert HEIC to JPG on Google Drive</h2>
                <div className="grid md:grid-cols-2 gap-8 items-center">
                  <div className="relative order-2 md:order-1">
                    <img 
                      src="https://image.heic-tojpg.com/how-to-convert-heic-to-jpg-on-google-drive.jpeg" 
                      alt="How to Convert Heic to Jpg on Google Drive" 
                      className="rounded-lg shadow-lg w-full h-[300px] object-cover"
                    />
                  </div>
                  <div className="space-y-4 order-1 md:order-2">
                    <ol className="list-decimal pl-5 space-y-3 text-gray-600">
                      <li>
                        <span className="font-medium">Download HEIC files</span> 
                      </li>
                      <li>
                        <span className="font-medium">Convert using our tool:</span> Visit our HEIC to JPG converter, upload the downloaded files.
                      </li>
                      <li>
                        <span className="font-medium">Download converted JPGs</span> 
                      </li>
                      <li>
                        <span className="font-medium">Re-upload (if needed):</span> Upload the converted JPG files back to Google Drive if desired.
                      </li>
                    </ol>
                  </div>
                </div>
              </div>
              
              {/* MacBook Guide */}
              <div>
                <h2 className="text-2xl font-bold mb-6 text-gray-800">How to convert HEIC to JPG on MacBook</h2>
                <div className="grid md:grid-cols-2 gap-8 items-center">
                  <div className="space-y-4">
                    <ol className="list-decimal pl-5 space-y-3 text-gray-600">
                      <li>
                        <span className="font-medium">Using Preview app:</span> Right-click the HEIC file, select "Open with" {">"}  "Preview", click "File" {">"}  "Export...", choose JPEG format.
                      </li>
                      <li>
                        <span className="font-medium">Adjust quality:</span> Use the quality slider to set your preferred level of compression before saving.
                      </li>
                      <li>
                        <span className="font-medium">Save converted file:</span> Click "Save" and your HEIC file will be converted and saved as JPG.
                      </li>
                      <li>
                        <span className="font-medium">Batch conversion:</span> For multiple files, use our online converter for faster batch processing.
                      </li>
                    </ol>
                  </div>
                  <div className="relative">
                    <img 
                      src="https://image.heic-tojpg.com/how-to-convert-heic-to-jpg-on-macbook.jpeg" 
                      alt="How to Convert Heic to Jpg on Macbook" 
                      className="rounded-lg shadow-lg w-full h-[300px] object-cover"
                    />
                  </div>
                </div>
              </div>
              
              {/* Chromebook Guide */}
              <div>
                <h2 className="text-2xl font-bold mb-6 text-gray-800">How to convert HEIC to JPG on Chromebook</h2>
                <div className="grid md:grid-cols-2 gap-8 items-center">
                  <div className="relative order-2 md:order-1">
                    <img 
                      src="https://image.heic-tojpg.com/how-to-convert-heic-to-jpg-on-chromebook.jpg" 
                      alt="How to Convert Heic to Jpg on Chromebook" 
                      className="rounded-lg shadow-lg w-full h-[300px] object-cover"
                    />
                  </div>
                  <div className="space-y-4 order-1 md:order-2">
                    <ol className="list-decimal pl-5 space-y-3 text-gray-600">
                      <li>
                        <span className="font-medium">Open our converter:</span> Launch Chrome browser and visit our HEIC to JPG converter website.
                      </li>
                      <li>
                        <span className="font-medium">Upload HEIC files</span>
                      </li>
                      <li>
                        <span className="font-medium">Set preferences:</span> Adjust quality settings and EXIF data options according to your needs.
                      </li>
                      <li>
                        <span className="font-medium">Download results.</span> 
                      </li>
                    </ol>
                  </div>
                </div>
              </div>
              
              {/* Without Software Guide */}
              <div>
                <h2 className="text-2xl font-bold mb-6 text-gray-800">How can I convert HEIC to JPG without software?</h2>
                <div className="grid md:grid-cols-2 gap-8 items-center">
                  <div className="space-y-4">
                    <ol className="list-decimal pl-5 space-y-3 text-gray-600">
                      <li>
                        <span className="font-medium">Use our HEIC to JPG converter:</span> Our online tool requires no software installation or downloads.
                      </li>
                      <li>
                        <span className="font-medium">Works in any browser:</span> Access our HEIC to JPG converter from Chrome, Firefox, Safari, Edge, or any modern browser.
                      </li>
                      <li>
                        <span className="font-medium">Simple upload process:</span> Drag and drop or click to select your HEIC files for HEIC to JPG converter.
                      </li>
                      <li>
                        <span className="font-medium">Instant conversion:</span> Get your JPG files immediately, with no software installation required at any step.
                      </li>
                    </ol>
                  </div>
                  <div className="relative">
                    <img 
                      src="https://cdn.pixabay.com/photo/2016/11/19/15/32/code-1839877_1280.jpg" 
                      alt="How Can I Convert Heic to Jpg Without Software" 
                      className="rounded-lg shadow-lg w-full h-[300px] object-cover"
                    />
                  </div>
                </div>
              </div>
              
              {/* iPad Guide */}
              <div>
                <h2 className="text-2xl font-bold mb-6 text-gray-800">How do I change HEIC to JPG on iPad?</h2>
                <div className="grid md:grid-cols-2 gap-8 items-center">
                  <div className="relative order-2 md:order-1">
                    <img 
                      src="https://cdn.pixabay.com/photo/2015/01/08/18/24/children-593313_1280.jpg" 
                      alt="Convert Heic to Jpg on iPad" 
                      className="rounded-lg shadow-lg w-full h-[300px] object-cover"
                    />
                  </div>
                  <div className="space-y-4 order-1 md:order-2">
                    <ol className="list-decimal pl-5 space-y-3 text-gray-600">
                      <li>
                        <span className="font-medium">Using Photos app:</span> Open the HEIC image in Photos app, tap the share button (square with arrow) in the bottom left.
                      </li>
                      <li>
                        <span className="font-medium">Copy and paste method:</span> Tap "Copy", then open Notes app and paste; the image will automatically convert to JPG.
                      </li>
                      <li>
                        <span className="font-medium">Using Files app:</span> Save the HEIC to Files.
                      </li>
                      <li>
                        <span className="font-medium">Using our HEIC to JPG converter website:</span> Open Safari on your iPad, visit our HEIC to JPG converter, and process multiple files in a single batch.
                      </li>
                    </ol>
                  </div>
                </div>
              </div>
            </section>

            <section className="why-use">
              <h2 className="text-2xl font-bold mb-6 text-gray-800">Why you should use a HEIC to JPG converter</h2>
              <div className="grid md:grid-cols-2 gap-8">
                <div>
                  <p className="text-gray-600">
                    Our HEIC to JPG converter offers several compelling advantages:
                  </p>
                  <ul className="mt-4 space-y-2 text-gray-600 list-disc pl-5">
                    <li>Free and unlimited conversions</li>
                    <li>No software installation required</li>
                    <li>Batch processing capability</li>
                    <li>Privacy-focused with secure file handling</li>
                    <li>High-quality output with adjustable settings</li>
                    <li>Works on all devices and platforms</li>
                    <li>Fast and efficient conversion process</li>
                  </ul>
                </div>
                <div className="relative">
                  <img 
                    src="https://cdn.pixabay.com/photo/2018/01/11/21/27/laptop-3076957_1280.jpg" 
                    alt="HEIC Converter Benefits" 
                    className="rounded-lg shadow-lg w-full h-[300px] object-cover"
                  />
                </div>
              </div>
            </section>

            <section className="faq">
              <h2 className="text-2xl font-bold mb-6 text-gray-800">FAQ</h2>
              <div className="grid md:grid-cols-2 gap-8">
                <div className="space-y-6">
                  <div>
                    <h3 className="text-lg font-semibold mb-2 text-gray-800">Is this HEIC to JPG converter free?</h3>
                    <p className="text-gray-600">
                      Yes, our HEIC to JPG converter is completely free.
                    </p>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold mb-2 text-gray-800">How many files can I use for the HEIC to JPG converter?</h3>
                    <p className="text-gray-600">
                      You can convert up to 100 files simultaneously in a single batch.
                    </p>
                  </div>
                </div>
                <div className="space-y-6">
                  <div>
                    <h3 className="text-lg font-semibold mb-2 text-gray-800">Are my files secure?</h3>
                    <p className="text-gray-600">
                      Yes, we process files securely and delete them automatically after conversion.
                    </p>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold mb-2 text-gray-800">What about image quality?</h3>
                    <p className="text-gray-600">
                      You can adjust the JPEG quality setting to balance between file size and image quality.
                    </p>
                  </div>
                </div>
              </div>
            </section>
          </div>
        </main>
      </>
    );
  }