'use client';

import { useState, useCallback, useEffect } from 'react';
import { useDropzone } from 'react-dropzone';
import { FiUpload, FiImage, FiDownload, FiArrowUp, FiX, FiCheck } from 'react-icons/fi';
import { Breadcrumb } from '@/components/Breadcrumb';
import Link from 'next/link';
import Head from 'next/head';
import Script from 'next/script';
import RelatedTools from '@/components/RelatedTools';

interface FileItem {
  id: string;
  name: string;
  size: number;
  status: 'waiting' | 'converting' | 'done' | 'error';
  downloadUrl?: string;
  errorMessage?: string;
  file: File;
}

export default function AvifToJpg() {
  const [files, setFiles] = useState<FileItem[]>([]);
  const [quality, setQuality] = useState(85);
  const [isConverting, setIsConverting] = useState(false);
  const [removeExif, setRemoveExif] = useState(false);
  const [consentPrivacy, setConsentPrivacy] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [convertProgress, setConvertProgress] = useState({ current: 0, total: 0 });
  const [showScrollTop, setShowScrollTop] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Add canonical link
  useEffect(() => {
    let link = document.querySelector('link[rel="canonical"]');
    
    if (!link) {
      link = document.createElement('link');
      link.setAttribute('rel', 'canonical');
      link.setAttribute('href', 'https://heic-tojpg.com/avif-to-jpg');
      document.head.appendChild(link);
    } else {
      link.setAttribute('href', 'https://heic-tojpg.com/avif-to-jpg');
    }
  }, []);

  // Detect if mobile device after component mount
  useEffect(() => {
    setIsMobile(/mobile|android|ios/i.test(window.navigator.userAgent));
  }, []);

  // Check scroll position to show/hide back to top button
  useEffect(() => {
    const handleScroll = () => {
      setShowScrollTop(window.scrollY > 400);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const onDrop = useCallback((acceptedFiles: File[]) => {
    setError(null);
    
    // Validate file types
    const invalidFiles = acceptedFiles.filter(file => {
      const lowerName = file.name.toLowerCase();
      return !lowerName.endsWith('.avif');
    });
    
    if (invalidFiles.length > 0) {
      setError('Please only upload AVIF files.');
      return;
    }

    const newFiles = acceptedFiles.map(file => ({
      id: Math.random().toString(36).substring(7),
      name: file.name,
      size: file.size,
      status: 'waiting' as const,
      file: file
    }));
    setFiles(prev => [...prev, ...newFiles]);
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/avif': ['.avif', '.AVIF']
    },
    maxFiles: 100,
    noDrag: isMobile,
    onDropRejected: () => {
      setError('Please only upload AVIF files.');
    },
    onError: (error) => {
      setError('An error occurred while uploading files.');
      console.error('Dropzone error:', error);
    }
  });

  const handleConvert = async () => {
    if (!consentPrivacy) {
      setError('Please consent to the privacy policy before converting files.');
      return;
    }

    if (files.length === 0) {
      setError('Please add some files to convert.');
      return;
    }

    setError(null);
    setIsConverting(true);
    const filesToConvert = files.filter(f => f.status === 'waiting');
    setConvertProgress({ current: 0, total: filesToConvert.length });
    
    try {
      await Promise.all(filesToConvert.map(async (fileItem, index) => {
        setFiles(prev => 
          prev.map(f => 
            f.id === fileItem.id 
              ? { ...f, status: 'converting' } 
              : f
          )
        );
        setConvertProgress(prev => ({ ...prev, current: index + 1 }));

        try {
          const formData = new FormData();
          formData.append('file', fileItem.file);
          formData.append('quality', quality.toString());
          formData.append('removeExif', removeExif.toString());
          formData.append('convertTo', 'jpg'); // Specify output format

          const response = await fetch('/api/avif-to-jpg', {
            method: 'POST',
            body: formData,
          });

          const result = await response.json();

          if (!response.ok) {
            const error = new Error(result.error || 'Conversion failed');
            // 添加详细信息到错误对象
            if (result.details) {
              (error as any).details = result.details;
            }
            throw error;
          }

          // 检查是否返回了错误图像
          if (result.success === false && result.errorImage === true) {
            setFiles(prev => 
              prev.map(f => 
                f.id === fileItem.id 
                  ? { 
                      ...f, 
                      status: 'error', 
                      errorMessage: 'This AVIF file format is not supported. Please try a different file.',
                      downloadUrl: result.url // 提供错误图像的下载链接
                    } 
                  : f
              )
            );
            setError(`Error converting ${fileItem.name}: File format not supported. An error image has been generated.`);
            return; // 提前返回，不执行下面的代码
          }

          setFiles(prev => 
            prev.map(f => 
              f.id === fileItem.id 
                ? { ...f, status: 'done', downloadUrl: result.url } 
                : f
            )
          );
        } catch (error: any) {
          console.error('File conversion error:', error);
          let errorMessage = error.message || 'Failed to convert file';
          
          // 检查是否有详细错误信息
          if (error.details) {
            errorMessage = `${errorMessage} - ${error.details}`;
          }
          
          setFiles(prev => 
            prev.map(f => 
              f.id === fileItem.id 
                ? { ...f, status: 'error', errorMessage: errorMessage } 
                : f
            )
          );
          setError(`Error converting ${fileItem.name}: ${errorMessage}`);
        }
      }));
    } catch (error: any) {
      console.error('Batch conversion error:', error);
      setError('An error occurred during batch conversion. Please try again.');
    } finally {
      setIsConverting(false);
    }
  };

  const handleDownloadAll = async () => {
    const completedFiles = files.filter(f => f.status === 'done' && f.downloadUrl);
    if (completedFiles.length === 0) {
      setError('No converted files to download');
      return;
    }

    setError(null);
    try {
      // Create a hidden download link to trigger download
      for (const file of completedFiles) {
        const link = document.createElement('a');
        link.href = file.downloadUrl!;
        link.download = file.name.replace('.avif', '').replace('.AVIF', '') + '.jpg';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        // Add small delay to prevent browser from blocking multiple downloads
        await new Promise(resolve => setTimeout(resolve, 500));
      }
    } catch (error) {
      console.error('Download error:', error);
      setError('Failed to download some files. Please try again.');
    }
  };

  const handleClearAll = () => {
    setFiles([]);
    setError(null);
  };

  const handleDeleteFile = (fileId: string) => {
    setFiles(prev => prev.filter(file => file.id !== fileId));
    setError(null);
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Scroll to top functionality
  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };

  return (
    <>
      <main className="min-h-screen p-4 md:p-8 max-w-4xl mx-auto relative bg-gradient-to-b from-slate-50/80 via-white to-white">
        <Breadcrumb />
        <h1 className="text-2xl md:text-4xl font-bold text-center mb-4 md:mb-8 text-gray-800">
          The Best Free AVIF to JPG Converter
        </h1>
        <p className="text-center text-gray-600 mb-6">
          Convert AVIF photos to standard JPG format online
        </p>

          {error && (
            <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg">
              <p className="text-red-600">{error}</p>
            </div>
          )}

          <div className="mb-6 md:mb-8 relative">
            <div
              {...getRootProps()}
              className={`border-2 border-dashed rounded-lg p-6 md:p-8 text-center cursor-pointer transition-all duration-300 relative backdrop-blur-sm
                ${isDragActive 
                  ? 'border-indigo-500 bg-gradient-to-br from-indigo-50/40 to-white/80' 
                  : 'border-gray-300 hover:border-indigo-400 hover:bg-gradient-to-br hover:from-slate-50/50 hover:to-white/90'
                } shadow-sm hover:shadow-md`}
            >
              <input {...getInputProps()} accept=".avif,.AVIF" />
              <FiImage className="mx-auto text-4xl md:text-5xl mb-3 text-indigo-400" />
              <p className="text-lg md:text-xl mb-2 text-gray-800">Select AVIF Photos</p>
              <p className="text-sm text-gray-600 mb-2">from your device</p>
              <p className="text-xs text-gray-500">Supports up to 100 files</p>
              
              {isMobile && (
                <div className="mt-4 text-sm text-indigo-600 font-medium">
                  Tap here to select photos from your device
                </div>
              )}
            </div>
            {/* Buy me a coffee button outside the dropzone in the left bottom corner */}
            <a
              href="https://ko-fi.com/yourfriendlycreator"
              target="_blank"
              rel="noopener noreferrer"
              className="absolute -bottom-10 left-0 px-2 py-1 md:px-3 md:py-2 text-xs md:text-sm border border-transparent rounded-md shadow-sm text-white bg-gradient-to-r from-pink-500 to-pink-600 hover:from-pink-600 hover:to-pink-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-pink-500 transition-all duration-200 flex items-center justify-center"
            >
              <span>☕Buy me a coffee</span>
            </a>
            <div className="absolute -bottom-10 left-[140px] md:left-[160px] text-xs md:text-sm text-gray-600 italic">
              I'll use the money to upgrade to a better server to help you with daily work.
            </div>
          </div>
          
          {files.length > 0 && (
            <div className="space-y-4 md:space-y-6">
              <div className="bg-white rounded-lg shadow-md overflow-x-auto border border-gray-100">
                <table className="min-w-full table-fixed">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="w-[10%] px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">#</th>
                      <th className="w-[30%] sm:w-[40%] px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">File</th>
                      <th className="w-[25%] sm:w-[20%] px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                      <th className="w-[25%] sm:w-[20%] px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Size</th>
                      <th className="w-[10%] px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Action</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {files.map((file, index) => (
                      <tr key={file.id}>
                        <td className="w-[10%] px-4 py-3 whitespace-nowrap text-sm text-gray-500">{index + 1}</td>
                        <td className="w-[30%] sm:w-[40%] px-4 py-3 text-sm font-medium text-gray-900">
                          <div className="truncate" title={file.name}>
                            {isMobile ? 
                              file.name.length > 10 ? 
                                file.name.slice(0, 7) + '...' + file.name.slice(-3) 
                                : file.name
                              : file.name
                            }
                          </div>
                        </td>
                        <td className="w-[25%] sm:w-[20%] px-4 py-3 whitespace-nowrap">
                          {file.status === 'done' && file.downloadUrl ? (
                            <a 
                              href={file.downloadUrl} 
                              className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                              download
                            >
                              Download
                            </a>
                          ) : file.status === 'error' ? (
                            <span className="text-red-500 text-sm" title={file.errorMessage}>Error</span>
                          ) : file.status === 'converting' ? (
                            <span className="text-yellow-500 text-sm">Converting...</span>
                          ) : (
                            <span className="text-gray-500 text-sm">Waiting</span>
                          )}
                        </td>
                        <td className="w-[25%] sm:w-[20%] px-4 py-3 whitespace-nowrap text-sm text-gray-500">{formatFileSize(file.size)}</td>
                        <td className="w-[10%] px-4 py-3 whitespace-nowrap text-center">
                          {file.status === 'done' ? (
                            <FiCheck className="text-green-500 w-5 h-5 mx-auto" title="Conversion Complete" />
                          ) : (
                            <button 
                              onClick={() => handleDeleteFile(file.id)}
                              className="text-gray-500 hover:text-red-600 transition-colors duration-200 focus:outline-none"
                              disabled={file.status === 'converting'}
                              title="Delete File"
                            >
                              <FiX className="w-5 h-5" />
                            </button>
                          )}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              <div className="bg-gradient-to-br from-slate-50/90 via-white to-white p-4 md:p-6 rounded-lg shadow-md border border-gray-100">
                <h2 className="text-lg md:text-xl font-semibold mb-4 text-gray-800">Output Settings</h2>
                <div className="grid gap-4 md:grid-cols-2">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Format:</label>
                    <select
                      className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                      value="JPG"
                      disabled
                    >
                      <option>JPG</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Quality:</label>
                    <div className="flex items-center space-x-2">
                      <input
                        type="range"
                        min="1"
                        max="100"
                        value={quality}
                        onChange={(e) => setQuality(parseInt(e.target.value))}
                        className="flex-1"
                      />
                      <span className="text-sm text-gray-600 w-12">{quality}%</span>
                    </div>
                  </div>
                </div>

                <div className="mt-4">
                  <label className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={removeExif}
                      onChange={(e) => setRemoveExif(e.target.checked)}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="text-sm text-gray-700">Remove all EXIF information</span>
                  </label>
                </div>
              </div>

              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <input
                    type="checkbox"
                    checked={consentPrivacy}
                    onChange={(e) => setConsentPrivacy(e.target.checked)}
                    className="mt-1 w-5 h-5 md:w-4 md:h-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                    required
                  />
                  <label className="text-sm text-gray-600">
                    I consent to heic-tojpg.com collecting and processing my data according to{' '}
                    <Link href="/privacy-policy" className="text-indigo-600 hover:text-indigo-800 underline">
                      Privacy Policy
                    </Link>
                    .
                  </label>
                </div>

                <div className="flex flex-col sm:flex-row gap-2 sm:justify-end sm:space-x-3">
                  <button
                    onClick={handleClearAll}
                    className="w-full sm:w-auto px-3 py-2 md:px-4 md:py-2 text-sm border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200"
                    disabled={isConverting}
                  >
                    Clear all
                  </button>
                  <button
                    onClick={handleConvert}
                    disabled={isConverting || !files.some(f => f.status === 'waiting') || !consentPrivacy}
                    className="w-full sm:w-auto px-3 py-2 md:px-4 md:py-2 text-sm border border-transparent rounded-md shadow-sm text-white bg-gradient-to-r from-indigo-600 to-indigo-700 hover:from-indigo-700 hover:to-indigo-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                  >
                    {isConverting ? 'Converting...' : 'Convert'}
                  </button>

                  <button
                    onClick={handleDownloadAll}
                    disabled={!files.some(f => f.status === 'done')}
                    className="w-full sm:w-auto px-3 py-2 md:px-4 md:py-2 text-sm border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-1 transition-colors duration-200"
                  >
                    <FiDownload className="w-4 h-4" />
                    <span>Download all</span>
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Share buttons */}
          <div className="mt-12 p-4 bg-gradient-to-br from-slate-50/90 via-white to-white rounded-lg shadow-md border border-gray-100">
            <h3 className="text-lg font-semibold mb-4 text-gray-800">Share with Friends</h3>
            <p className="text-sm text-gray-600 mb-4">If you found this tool useful, please share it with friends who might need it!</p>
            <div className="sharethis-inline-share-buttons"></div>
          </div>
          {/* Related tools */}
          <RelatedTools currentTool="AVIF to JPG" />

          {/* Conversion progress indicator */}
          {isConverting && isMobile && (
            <div className="fixed bottom-4 left-0 right-0 mx-4 bg-gradient-to-r from-indigo-50/90 via-white to-blue-50/90 p-4 rounded-lg shadow-lg border border-indigo-100 backdrop-blur-sm">
              <div className="text-center text-sm text-indigo-800 font-medium">
                Converting: {convertProgress.current} of {convertProgress.total} files
              </div>
              <div className="mt-2 h-2 bg-indigo-100 rounded-full overflow-hidden">
                <div 
                  className="h-full bg-gradient-to-r from-indigo-600 to-indigo-500 transition-all duration-300" 
                  style={{ width: `${(convertProgress.current / convertProgress.total) * 100}%` }}
                />
              </div>
            </div>
          )}

          <div className="mt-12 space-y-16">
            <section className="introduction">
              <h2 className="text-2xl font-bold mb-6 text-gray-800">
                <a href="/avif-to-jpg" className="hover:text-indigo-600 transition-colors">AVIF to JPG</a> Converter Features
              </h2>
              <div className="space-y-16">
                {/* Feature Group 1: Free & Easy + Fast Conversion */}
                <div className="grid md:grid-cols-2 gap-8 items-center">
                  <div className="space-y-8">
                    <div>
                      <h3 className="text-lg font-semibold mb-3 text-gray-800 flex items-center">
                        <span className="bg-indigo-100 w-8 h-8 rounded-full flex items-center justify-center mr-3 flex-shrink-0">1</span>
                        Professional AVIF to JPG Conversion - 100% Free
                      </h3>
                      <ul className="space-y-2 text-gray-600 list-disc pl-11">
                        <li>No registration or payment required - convert AVIF to JPG with enterprise-grade quality using advanced codec optimization</li>
                        <li>Intuitive drag-and-drop interface with multi-threaded processing for efficient batch conversion from .AVIF to JPG format</li>
                      </ul>
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold mb-3 text-gray-800 flex items-center">
                        <span className="bg-indigo-100 w-8 h-8 rounded-full flex items-center justify-center mr-3 flex-shrink-0">2</span>
                        High-Speed AVIF to JPG Converter with Enhanced Quality Preservation
                      </h3>
                      <ul className="space-y-2 text-gray-600 list-disc pl-11">
                        <li>Utilizing advanced chroma subsampling (4:4:4) and MozJPEG compression for superior AVIF file to JPG conversion</li>
                        <li>Maintains optimal bit depth and color accuracy with ICC profile preservation when changing AVIF to JPG</li>
                      </ul>
                    </div>
                  </div>
                  <div className="relative">
                    <img 
                      src="https://image.heic-tojpg.com/avif-to-jpg-converter-tool.webp" 
                      alt="Professional AVIF to JPG Converter Tool" 
                      className="rounded-lg shadow-lg w-full h-[400px] object-cover"
                    />
                  </div>
                </div>

                {/* Feature Group 2: Security & Privacy */}
                <div className="grid md:grid-cols-2 gap-8 items-center">
                  <div className="relative order-2 md:order-1">
                    <img 
                      src="https://cdn.pixabay.com/photo/2018/01/17/20/22/analytics-3088958_1280.jpg" 
                      alt="Online Convert AVIF to JPG Format Converter" 
                      className="rounded-lg shadow-lg w-full h-[400px] object-cover"
                    />
                  </div>
                  <div className="space-y-8 order-1 md:order-2">
                    <div>
                      <h3 className="text-lg font-semibold mb-3 text-gray-800 flex items-center">
                        <span className="bg-indigo-100 w-8 h-8 rounded-full flex items-center justify-center mr-3 flex-shrink-0">3</span>
                        Watermark-Free & Unlimited AVIF to JPG Converter
                      </h3>
                      <ul className="space-y-2 text-gray-600 list-disc pl-11">
                        <li>Our converter AVIF to JPG produces completely watermark-free JPG files, ready for professional design projects, websites, or printing</li>
                        <li>No file size limits, no quantity restrictions - change AVIF to JPG with lossless quality preservation technology</li>
                      </ul>
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold mb-3 text-gray-800 flex items-center">
                        <span className="bg-indigo-100 w-8 h-8 rounded-full flex items-center justify-center mr-3 flex-shrink-0">4</span>
                        AVIF to JPG – End-to-End Encryption & Privacy Protection
                      </h3>
                      <ul className="space-y-2 text-gray-600 list-disc pl-11">
                        <li>Employing AES-256 bit encryption standards to ensure AVIF file security during transmission and processing</li>
                        <li>Using convert-and-delete technology - files are immediately removed from servers after .AVIF to JPG conversion</li>
                      </ul>
                    </div>
                  </div>
                </div>

                {/* Feature Group 3: Batch Processing & Compatibility */}
                <div className="grid md:grid-cols-2 gap-8 items-center">
                  <div className="space-y-8">
                    <div>
                      <h3 className="text-lg font-semibold mb-3 text-gray-800 flex items-center">
                        <span className="bg-indigo-100 w-8 h-8 rounded-full flex items-center justify-center mr-3 flex-shrink-0">5</span>
                        Efficient Batch AVIF to JPG Conversion Technology
                      </h3>
                      <ul className="space-y-2 text-gray-600 list-disc pl-11">
                        <li>Multi-threaded FFmpeg integration to simultaneously convert multiple AVIF files to JPG format with superior quality</li>
                        <li>Perfect for UX designers and developers who need a reliable AVIF to JPG converter for cross-platform compatibility</li>
                      </ul>
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold mb-3 text-gray-800 flex items-center">
                        <span className="bg-indigo-100 w-8 h-8 rounded-full flex items-center justify-center mr-3 flex-shrink-0">6</span>
                        Cross-Platform AVIF to JPG Conversion Compatibility
                      </h3>
                      <ul className="space-y-2 text-gray-600 list-disc pl-11">
                        <li>Our AVIF to JPG converter supports all major browsers including Chrome, Firefox, Edge, Safari and more</li>
                        <li>Compatible with Windows, macOS, Linux, Android, and iOS devices - a truly universal converter AVIF to JPG solution</li>
                      </ul>
                    </div>
                  </div>
                  <div className="relative">
                    <img 
                      src="https://cdn.pixabay.com/photo/2016/03/27/18/54/technology-1283624_1280.jpg" 
                      alt="Batch AVIF to JPG Conversion Compatibility" 
                      className="rounded-lg shadow-lg w-full h-[400px] object-cover"
                    />
                  </div>
                </div>

                {/* Feature Group 4: Quality Control & Online Access */}
                <div className="grid md:grid-cols-2 gap-8 items-center">
                  <div className="relative order-2 md:order-1">
                    <img 
                      src="https://cdn.pixabay.com/photo/2016/11/29/06/18/home-office-1867761_1280.jpg" 
                      alt="Online AVIF to JPG Quality Control" 
                      className="rounded-lg shadow-lg w-full h-[400px] object-cover"
                    />
                  </div>
                  <div className="space-y-8 order-1 md:order-2">
                    <div>
                      <h3 className="text-lg font-semibold mb-3 text-gray-800 flex items-center">
                        <span className="bg-indigo-100 w-8 h-8 rounded-full flex items-center justify-center mr-3 flex-shrink-0">7</span>
                        Professional AVIF to JPG Converter with Advanced Image Processing
                      </h3>
                      <ul className="space-y-2 text-gray-600 list-disc pl-11">
                        <li>Optimized JPG compression parameters with DCT quantization matrix tuning for superior AVIF file to JPG conversion</li>
                        <li>Supports color profile management and gamma correction for precise color reproduction when you convert AVIF to JPG</li>
                      </ul>
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold mb-3 text-gray-800 flex items-center">
                        <span className="bg-indigo-100 w-8 h-8 rounded-full flex items-center justify-center mr-3 flex-shrink-0">8</span>
                        Cloud-Based AVIF to JPG Conversion - No Software Installation Required
                      </h3>
                      <ul className="space-y-2 text-gray-600 list-disc pl-11">
                        <li>Pure cloud processing with server-side FFmpeg optimization - no software or plugins needed to change AVIF to JPG files</li>
                        <li>WebAssembly and Sharp library integration for efficient browser-based AVIF to JPG processing on any device</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </section>

            <section className="what-is">
              <h2 className="text-2xl font-bold mb-6 text-gray-800">Understanding the AVIF to JPG Converter: Brand DNA Analysis</h2>
              
              <div className="space-y-16">
                {/* AVIF Introduction Group */}
                <div className="grid md:grid-cols-2 gap-8 items-center">
                  <div className="space-y-8">
                    <div>
                      <h3 className="text-lg font-semibold mb-3 text-gray-800">What is AVIF to JPG Conversion?</h3>
                      <p className="text-gray-600">
                        AVIF to JPG conversion is the process of transforming AV1 Image File Format (AVIF) into the universally compatible JPEG format. While AVIF offers excellent compression using AV1 video codec technology,
                        not all software and platforms support it, making JPG a more widely compatible choice. Our specialized AVIF to JPG converter ensures optimal preservation of image quality during the conversion process.
                      </p>
                    </div>

                    <div>
                      <h3 className="text-lg font-semibold mb-3 text-gray-800">What is the AVIF Format?</h3>
                      <p className="text-gray-600">
                        AVIF is a modern image format developed by the Alliance for Open Media that provides superior compression for web images while maintaining high quality. It uses both lossy compression (based on the AV1 video codec) and lossless compression techniques,
                        achieving file sizes 30-50% smaller than comparable JPG files. Despite these advantages, many design applications and older systems still require JPG format, necessitating a reliable .AVIF to JPG converter. The AVIF format supports HDR, wide color gamut, and up to 12-bit color depth, making conversion to standard formats essential for broader compatibility. vist <a href="https://en.wikipedia.org/wiki/AVIF" target="_blank" rel="noopener noreferrer" className="text-indigo-600 hover:text-indigo-800 underline">wikipedia on AVIF</a>.
                      </p>
                    </div>
                  </div>
                  <div className="relative">
                    <img 
                      src="https://image.heic-tojpg.com/wikipedia-on-avif.webp" 
                      alt="Professional Analysis of AVIF Format" 
                      className="rounded-lg shadow-lg w-full h-[400px] object-cover"
                    />
                  </div>
                </div>

                {/* JPG Details Group */}
                <div className="grid md:grid-cols-2 gap-8 items-center">
                  <div className="relative order-2 md:order-1">
                    <img 
                      src="https://image.heic-tojpg.com/what-is-jpeg.jpg" 
                      alt="Detailed Explanation of JPG Files" 
                      className="rounded-lg shadow-lg w-full h-[400px] object-cover"
                    />
                  </div>
                  <div className="space-y-8 order-1 md:order-2">
                    <div>
                      <h3 className="text-lg font-semibold mb-3 text-gray-800">What is a JPG File?</h3>
                      <p className="text-gray-600">
                        JPG (JPEG - Joint Photographic Experts Group) is a widely used raster image format that utilizes lossy compression to create smaller file sizes. It employs discrete cosine transform (DCT) algorithms and supports up to 16.7 million colors, making it ideal for photographs and complex images.
                        JPG files are universally supported across all platforms and applications, making them essential when you need to convert AVIF to JPG for maximum compatibility. The JPG format's widespread adoption since 1992 has made it the standard for digital photography and web imagery.
                      </p>
                    </div>

                    <div>
                      <h3 className="text-lg font-semibold mb-3 text-gray-800">What is Metadata in Image Files?</h3>
                      <p className="text-gray-600">
                        Image metadata includes EXIF (Exchangeable Image File Format) information such as creation date, camera settings, device information, and sometimes GPS location data. When using our AVIF to JPG converter, 
                        you can choose to preserve or remove this metadata during the conversion process. Removing metadata can be crucial for privacy protection and reducing file size when you convert AVIF file to JPG format, while preserving it maintains valuable photographic information.
                      </p>
                    </div>
                  </div>
                </div>

                {/* Format Comparison Group - User Pain Point Mapping */}
                <div className="grid md:grid-cols-2 gap-8 items-center">
                  <div className="space-y-8">
                    <div>
                      <h3 className="text-lg font-semibold mb-3 text-gray-800">AVIF vs. JPG: Understanding the Differences</h3>
                      <p className="text-gray-600">
                        While AVIF offers superior compression using advanced AV1 encoding techniques, JPG provides universal compatibility with its widely supported format. 
                        AVIF can be significantly smaller than JPGs while maintaining similar visual quality, but JPG's widespread support makes it essential to convert AVIF to JPG for certain applications and platforms. Our converter AVIF to JPG addresses this exact pain point for users.
                      </p>
                    </div>

                    <div>
                      <h3 className="font-semibold text-gray-900">What's the difference between AVIF and JPG?</h3>
                      <p className="mt-1 text-gray-700">
                        AVIF is a modern image format that offers superior compression using the AV1 codec with both lossy and lossless capabilities. JPG is an established format that uses DCT-based lossy compression exclusively.
                        While AVIF files are typically 30-50% smaller than JPG files of equivalent quality and support HDR and wide color gamut, JPG offers better compatibility across all platforms and applications, which is why many users need to convert AVIF to JPG.
                      </p>
                    </div>
                  </div>
                  <div className="relative">
                    <img 
                      src="https://image.heic-tojpg.com/metadata-in-image.webp" 
                      alt="AVIF vs JPG Format Comparison" 
                      className="rounded-lg shadow-lg w-full h-[400px] object-cover"
                    />
                  </div>
                </div>

                {/* Conversion Benefits Group - Value Proposition Extraction */}
                <div className="grid md:grid-cols-2 gap-8 items-center">
                  <div className="relative order-2 md:order-1">
                    <img 
                      src="https://cdn.pixabay.com/photo/2015/01/08/18/27/startup-593341_1280.jpg" 
                      alt="Benefits of AVIF to JPG Conversion" 
                      className="rounded-lg shadow-lg w-full h-[400px] object-cover"
                    />
                  </div>
                  <div className="space-y-8 order-1 md:order-2">
                    <div>
                      <h3 className="text-lg font-semibold mb-3 text-gray-800">Why Convert AVIF to JPG?</h3>
                      <p className="text-gray-600">
                        Converting AVIF to JPG ensures maximum compatibility across all applications, platforms, and devices. While AVIF offers excellent compression, many graphic design applications, content management systems, and older browsers don't fully support AVIF.
                        Using our AVIF to JPG converter provides universal compatibility, eliminating potential issues when editing or sharing your images. This is particularly important for professional workflows where software compatibility is essential.
                      </p>
                    </div>

                    <div>
                      <h3 className="text-lg font-semibold mb-3 text-gray-800">Technical Benefits of JPG Format</h3>
                      <p className="text-gray-600">
                        JPG files offer several technical advantages including efficient storage, fast loading times, and universal support across all image editing software. When you convert .AVIF to JPG, you gain access to these benefits
                        plus widespread compatibility with virtually all image editing software, web platforms, and operating systems, making JPG an excellent universal format for both web and print applications where AVIF support may be limited.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </section>

            <section className="how-to">
              <h2 className="text-2xl font-bold mb-6 text-gray-800">How to Use the AVIF to JPG Converter - Differential Positioning</h2>
              
              <div className="grid md:grid-cols-2 gap-8 items-center mb-12">
                <div>
                  <ol className="space-y-6 relative">
                    <div className="absolute top-0 bottom-0 left-4 w-0.5 bg-indigo-100"></div>
                    <li className="relative pl-12">
                      <div className="absolute left-0 bg-indigo-500 text-white w-8 h-8 rounded-full flex items-center justify-center font-semibold">1</div>
                      <h3 className="text-lg font-semibold mb-2 text-gray-800">Upload AVIF Files</h3>
                      <p className="text-gray-600">
                        Drag and drop your AVIF files into the conversion area, or click to select files from your device. Our AVIF to JPG converter supports batch uploading of multiple files for simultaneous processing, increasing efficiency for photographers and designers.
                      </p>
                    </li>
                    <li className="relative pl-12">
                      <div className="absolute left-0 bg-indigo-500 text-white w-8 h-8 rounded-full flex items-center justify-center font-semibold">2</div>
                      <h3 className="text-lg font-semibold mb-2 text-gray-800">Choose Conversion Settings</h3>
                      <p className="text-gray-600">
                        Adjust AVIF to JPG converter settings to optimize your output. You can select JPG quality (up to 100%) and choose to preserve or remove EXIF metadata from your images for enhanced privacy protection when converting from AVIF file to JPG.
                      </p>
                    </li>
                    <li className="relative pl-12">
                      <div className="absolute left-0 bg-indigo-500 text-white w-8 h-8 rounded-full flex items-center justify-center font-semibold">3</div>
                      <h3 className="text-lg font-semibold mb-2 text-gray-800">Convert and Download</h3>
                      <p className="text-gray-600">
                        Click the "Convert" button to start the AVIF to JPG conversion process using our advanced FFmpeg and Sharp library integration. Once completed, you can download JPG files individually or use our batch download option to download all converted files at once.
                      </p>
                    </li>
                  </ol>
                </div>
                <div className="relative">
                  <img 
                    src="https://cdn.pixabay.com/photo/2015/05/31/10/55/man-791049_1280.jpg" 
                    alt="AVIF to JPG Conversion Process" 
                    className="rounded-lg shadow-lg w-full h-[400px] object-cover"
                  />
                </div>
              </div>
            </section>
          </div>

          
          <section className="why-use mb-8 mt-12">
            <h2 className="text-2xl font-bold mb-6 text-gray-800">Why Choose Our AVIF to JPG Converter</h2>
            
            <div className="space-y-16">
              {/* Reason 1 */}
              <div className="grid md:grid-cols-2 gap-8 items-center">
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-gray-800">Universal Compatibility</h3>
                  <p className="text-gray-600">
                    While AVIF offers excellent compression using the advanced AV1 codec, many applications and devices don't fully support this format. Our AVIF to JPG converter ensures your images can be viewed, edited, and shared across all platforms without compatibility issues.
                    The conversion process preserves all image attributes including color profiles and optimal bit depth representation for the highest quality output.
                  </p>
                  <p className="text-gray-600">
                    Our AVIF to JPG converter maintains optimal image quality while changing the file format, using advanced DCT optimization and chroma subsampling techniques to provide the highest fidelity conversion possible from AVIF file to JPG.
                  </p>
                </div>
                <div className="relative">
                  <img 
                    src="https://cdn.pixabay.com/photo/2016/11/23/14/37/blur-1853262_1280.jpg" 
                    alt="AVIF to JPG Universal Compatibility" 
                    className="rounded-lg shadow-lg w-full h-[300px] object-cover"
                  />
                </div>
              </div>
              
              {/* Reason 2 */}
              <div className="grid md:grid-cols-2 gap-8 items-center">
                <div className="relative order-2 md:order-1">
                  <img 
                    src="https://cdn.pixabay.com/photo/2015/07/17/22/43/student-849825_1280.jpg" 
                    alt="Simplified AVIF to JPG Workflow" 
                    className="rounded-lg shadow-lg w-full h-[300px] object-cover"
                  />
                </div>
                <div className="space-y-4 order-1 md:order-2">
                  <h3 className="text-lg font-semibold text-gray-800">Simplified Design Workflow</h3>
                  <p className="text-gray-600">
                    When working with image files across multiple applications, using a consistent file format can streamline your workflow. Converting AVIF to JPG eliminates compatibility issues when importing images into different design software or content management systems,
                    especially platforms that don't fully support the AVIF MIME type or lack AV1 codec integration.
                  </p>
                  <p className="text-gray-600">
                    Our AVIF to JPG converter's batch processing feature allows you to convert multiple AVIF files to JPG simultaneously, supporting parallel multi-thread processing that saves valuable time and effort in your design or development workflow.
                  </p>
                </div>
              </div>
              
              {/* Reason 3 */}
              <div className="grid md:grid-cols-2 gap-8 items-center">
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-gray-800">Privacy and Security</h3>
                  <p className="text-gray-600">
                    Using our AVIF to JPG converter tool, you can choose to remove EXIF metadata from your images during the conversion process. This feature is particularly important for privacy-conscious users, 
                    allowing you to share images without revealing sensitive information such as GPS coordinates or device details when you change AVIF to JPG format.
                  </p>
                  <p className="text-gray-600">
                    Our secure conversion infrastructure employs TLS encryption and secure file handling protocols, ensuring your images remain private throughout the AVIF to JPG conversion process. All uploaded files are automatically deleted after processing,
                    providing peace of mind for security-conscious users who need to convert AVIF file to JPG.
                  </p>
                </div>
                <div className="relative">
                  <img 
                    src="https://cdn.pixabay.com/photo/2017/10/10/21/46/laptop-2838917_1280.jpg" 
                    alt="AVIF to JPG Privacy Protection" 
                    className="rounded-lg shadow-lg w-full h-[300px] object-cover"
                  />
                </div>
              </div>
              
              {/* Reason 4 */}
              <div className="grid md:grid-cols-2 gap-8 items-center">
                <div className="relative order-2 md:order-1">
                  <img 
                    src="https://image.heic-tojpg.com/jfif-to-jpg-web-optimization.webp" 
                    alt="AVIF to JPG Quality Preservation" 
                    className="rounded-lg shadow-lg w-full h-[300px] object-cover"
                  />
                </div>
                <div className="space-y-4 order-1 md:order-2">
                  <h3 className="text-lg font-semibold text-gray-800">Professional Image Quality</h3>
                  <p className="text-gray-600">
                    Our AVIF to JPG converter uses advanced image processing algorithms including FFmpeg integration to ensure the highest quality output. The conversion process preserves optimal color representation and detail,
                    making it ideal for professional photographers, graphic designers, web developers, and digital artists who need to convert AVIF to JPG without significant quality loss.
                  </p>
                  <p className="text-gray-600">
                    The JPG format's efficient compression ensures that visual details are preserved when you convert AVIF to JPG, making it perfect for images that require excellent visual quality such as product photography, portraits, landscapes, and creative works that need wide distribution.
                  </p>
                </div>
              </div>
            </div>
          </section>

          <div className="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 className="text-xl font-bold mb-4">Frequently Asked Questions About AVIF to JPG Conversion</h2>
            <div className="space-y-6">
              <div>
                <h3 className="font-semibold text-gray-900">What's the difference between AVIF and JPG?</h3>
                <p className="mt-1 text-gray-700">
                  AVIF is a modern image format developed by the Alliance for Open Media that offers superior compression using the AV1 video codec with both lossy and lossless capabilities. JPG is an established format that uses DCT-based lossy compression exclusively.
                  While AVIF files are typically 30-50% smaller than JPG files of equivalent quality and support HDR and wide color gamut, JPG offers better compatibility across all platforms and applications, which is why many users need to convert AVIF to JPG.
                </p>
              </div>
              <div>
                <h3 className="font-semibold text-gray-900">Will I lose quality when converting AVIF to JPG?</h3>
                <p className="mt-1 text-gray-700">
                  When converting from AVIF to JPG using our converter, there may be some quality adjustment as JPG uses lossy DCT-based compression. However, our AVIF to JPG converter employs advanced FFmpeg integration and optimized compression parameters to maintain the highest possible image quality
                  while ensuring compatibility. Our converter ensures optimal image fidelity through advanced processing algorithms and MozJPEG optimization techniques when converting .AVIF to JPG.
                </p>
              </div>
              <div>
                <h3 className="font-semibold text-gray-900">Is it safe to convert AVIF to JPG online?</h3>
                <p className="mt-1 text-gray-700">
                  Yes, our online AVIF to JPG converter follows strict security protocols when handling all files. Your images are briefly processed on our secure servers and then automatically deleted.
                  We don't permanently store your uploaded files or use them for any other purpose. All conversion processes take place in a TLS-encrypted secure environment, ensuring your AVIF file to JPG conversion
                  is completely safe and reliable.
                </p>
              </div>
            </div>
          </div>
      </main>

      {showScrollTop && (
        <button
          onClick={scrollToTop}
          className="fixed bottom-6 right-6 p-3 bg-gradient-to-r from-indigo-600 to-indigo-700 text-white rounded-full shadow-lg hover:from-indigo-700 hover:to-indigo-800 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 z-50"
          aria-label="Scroll to top"
        >
          <FiArrowUp className="w-6 h-6" />
        </button>
      )}
    </>
  );
} 